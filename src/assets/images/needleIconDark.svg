<svg width="87" height="20" viewBox="0 0 87 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_726_26090)">
<path d="M14.9084 3V15L84.9411 10.2007C86.1797 10.1158 86.1849 8.29783 84.9468 8.20581L14.9084 3Z" fill="url(#paint0_linear_726_26090)"/>
</g>
<g filter="url(#filter1_d_726_26090)">
<path d="M22.6453 12L83.4348 9.4484L22.6453 5.25V12Z" fill="url(#paint1_linear_726_26090)"/>
</g>
<g filter="url(#filter2_d_726_26090)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.96338 9.00285C4.96338 13.9709 8.99119 18 13.9634 18C18.9356 18 22.9634 13.9709 22.9634 9.00285C22.9634 4.02909 18.9356 0 13.9634 0C8.99119 0 4.96338 4.02909 4.96338 9.00285Z" fill="url(#paint2_linear_726_26090)"/>
</g>
<g filter="url(#filter3_d_726_26090)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.50467 9.00254C7.50467 12.5678 10.3952 15.4592 13.9634 15.4592C17.5316 15.4592 20.4221 12.5678 20.4221 9.00254C20.4221 5.4332 17.5316 2.54178 13.9634 2.54178C10.3952 2.54178 7.50467 5.4332 7.50467 9.00254Z" fill="url(#paint3_linear_726_26090)"/>
</g>
<defs>
<filter id="filter0_d_726_26090" x="8.28994" y="3" width="79.7889" height="20.8247" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.20617" dy="4.41234"/>
<feGaussianBlur stdDeviation="2.20617"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_726_26090"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_726_26090" result="shape"/>
</filter>
<filter id="filter1_d_726_26090" x="16.0269" y="5.25" width="69.6141" height="15.5747" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.20617" dy="4.41234"/>
<feGaussianBlur stdDeviation="2.20617"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_726_26090"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_726_26090" result="shape"/>
</filter>
<filter id="filter2_d_726_26090" x="0.368393" y="0" width="24.1266" height="24.1266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.53166" dy="3.06332"/>
<feGaussianBlur stdDeviation="1.53166"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_726_26090"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_726_26090" result="shape"/>
</filter>
<filter id="filter3_d_726_26090" x="2.90965" y="2.54178" width="19.0441" height="19.0441" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.53166" dy="3.06332"/>
<feGaussianBlur stdDeviation="1.53166"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_726_26090"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_726_26090" result="shape"/>
</filter>
<linearGradient id="paint0_linear_726_26090" x1="108.325" y1="9" x2="22.3475" y2="8.99998" gradientUnits="userSpaceOnUse">
<stop offset="0.111431" stop-color="#737373"/>
<stop offset="1" stop-color="#303030"/>
</linearGradient>
<linearGradient id="paint1_linear_726_26090" x1="101.633" y1="9.47273" x2="3.588" y2="7.632" gradientUnits="userSpaceOnUse">
<stop stop-color="#303030"/>
<stop offset="1" stop-color="#737373"/>
</linearGradient>
<linearGradient id="paint2_linear_726_26090" x1="13.9634" y1="-13.6041" x2="13.9634" y2="32.9494" gradientUnits="userSpaceOnUse">
<stop offset="0.389745" stop-color="#303030"/>
<stop offset="1" stop-color="#737373"/>
</linearGradient>
<linearGradient id="paint3_linear_726_26090" x1="13.9634" y1="-3.03548" x2="13.9634" y2="17.4077" gradientUnits="userSpaceOnUse">
<stop offset="0.104436" stop-color="#737373"/>
<stop offset="0.752891" stop-color="#303030"/>
</linearGradient>
</defs>
</svg>
