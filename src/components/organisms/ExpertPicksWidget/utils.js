import { EXPERT_PICKS_API } from '../../../config/urlConfig';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../../../utils/apiUtil';

export const getExpertPicksFilters = async (params = {}) => {
  const apiUrl = EXPERT_PICKS_API.GET_FILTERS;
  const headers = {
    ...getGenericAppHeaders(),
  };

  try {
    const response = await makeApiGetCall({
      url: apiUrl,
      headers,
      queryParams: params,
    });

    if (response?.data) {
      return response.data;
    }
    throw new Error('Failed to fetch expert picks filters');
  } catch (error) {
    console.error('Error fetching expert picks filters:', error);
    throw error;
  }
};

export const getScripsList = async (data = {}) => {
  const url = EXPERT_PICKS_API.GET_SCRIPS_LIST;
  const headers = {
    ...getGenericAppHeaders(),
  };

  try {
    const filterList = data.data.results[0]['filter-list'];

    // hard-code requestBody, except analyst_count::to
    const requestBody = {
      instrumentType: 'ES',
      'page-size': 20,
      'filter-list': {
        mcap: ['Small Cap', 'Mid Cap', 'Large Cap'],
      },
      'range-list': {
        analyst_count: {
          from: 8.0,
          to: filterList?.anlyst_cnt_range?.max,
        },
        buy_percentage: {
          from: 75.0,
          to: 100,
        },
      },
      sort: {
        'sort-param': 'buy_percentage',
        order: 'DESC',
      },
      search_after_keys: [],
    };

    const response = await makeApiPostCall({
      url,
      headers,
      body: requestBody,
    });

    if (response?.data) {
      return response.data;
    }
    throw new Error('Failed to fetch scrips list');
  } catch (error) {
    console.error('Error fetching scrips list:', error);
    throw error;
  }
};
