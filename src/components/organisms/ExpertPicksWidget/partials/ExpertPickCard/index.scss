body {
  margin: 0;
}

.card {
  border-radius: 16px;
  background: var(--surface-level-1);
  background-size: cover;
  background-position: center;
  margin: 12px 16px;
  margin-bottom: 0px;
  width: calc(100vw - 32px);
}

.firstFullPageCard {
  margin-top: 16px;
}

.partialCard {
  margin-right: 0px;
  width: calc(80vw);
}

.lastCard {
  width: calc(100vw - 32px);
}

.headerWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  padding-bottom: 0px;

  .companyDetails {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-neutral-weak);

    .stockHeader {
      display: flex;
      width: 100%;
      height: 38px;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      overflow: hidden !important;
    }
    .title {
      font-size: 12px;
      color: var(--text-neutral-strong);
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
      width: 100%;
      font-weight: 400;
      line-height: 16px;
    }

    .rejectedText {
      color: var(--text-negative-strong);
    }

    .companyIcon {
      background: #fff;
      display: flex;
      align-items: center;
      margin-right: 10px;
      justify-content: center;
      min-width: 38px;
      min-height: 38px;
      border-radius: 8px;
      overflow: hidden;

      > img {
        width: 30px;
        height: 30px;
      }
    }

    .companyName {
      font-size: 14px;
      color: var(--text-neutral-strong);
      font-weight: 400;
      line-height: 20px;
      width: 100%;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
      display: flex;
    }
  }

  .addFundsCtaContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
  }

  .addFundsWrapper {
    width: 100%;
    display: flex;
    align-items: center;

    .addFundsText {
      font-size: 14px;
      color: var(--text-neutral-strong);
      font-weight: 400;
      line-height: 20px;
      margin-right: 4px;
    }

    .addFundsAmount {
      font-size: 14px;
      color: var(--text-neutral-strong);
      font-weight: 600;
      line-height: 20px;
    }
  }

  .returnsCtaContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    @media only screen and (max-width: 375px) {
      gap: 4px;
    }
  }

  .addFundsWrapper {
    width: 100%;
    display: flex;
    align-items: center;
  }

  .cta {
    background: var(--background-primary-strong);
    min-width: 94px;
    width: max-content;
    padding: 0 20px;
    height: 32px;
    border-radius: 18px;

    @media only screen and (max-width: 375px) {
      height: 28px;
      padding: 0 10px;
    }

    > span {
      color: var(--text-universal-strong) !important;
      align-content: center;
    }
  }

  .greenButton {
    background: var(--background-positive-strong) !important;
    border: none;
  }

  .redButton {
    background: var(--background-negative-strong) !important;
    border: none;
  }

  .disabledButton {
    opacity: 0.5;
  }

  .buttonTextClassName {
    font-size: 12px !important;
    line-height: 16px;
    color: var(--text-primary-strong);
    font-weight: 600;
  }

  .stockPriceChange {
    font-size: 12px !important;
    overflow: hidden;
    font-weight: 400;
    line-height: 16px;

    @media only screen and (max-width: 375px) {
      font-size: 12px !important;
    }
  }

  .returnsWrapper {
    display: flex;
    align-items: center;
    // gap: 8px;

    @media only screen and (max-width: 375px) {
      gap: 4px;
    }
  }

  .returnText {
    color: var(--text-neutral-medium);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;

    @media only screen and (max-width: 375px) {
      font-size: 12px;
    }
  }

  .mtfChip {
    display: inline-block;
    background: linear-gradient(270deg, #2f81ed 0%, #00b8f5 90.5%);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    margin-right: 8px;
    color: var(--text-universal-strong);
  }
}

.descriptionText {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  color: var(--text-neutral-strong);
  text-align: center;
  display: block;
}

.subtitleText {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  color: var(--text-neutral-strong);
  display: block;
}
.sipReturnsRow {
  flex-direction: row;
}
//// to set all button same size
.reminderCardButton {
  // width: 94px;
  // height: 32px;
  min-width: 94px;
  max-width: 94px;
  white-space: nowrap;
  font-size: 12px !important;
  color: var(--text-primary-strong);
  font-weight: 600;
}

.descriptionWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 12px;
}

.expertsRecommendation {
  display: flex;
  align-items: center;
  gap: 4px;
}

.expertsRecommendationText {
  color: var(--text-neutral-strong);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}
