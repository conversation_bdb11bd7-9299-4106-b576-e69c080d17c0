import { useMemo } from 'react';
import cx from 'classnames';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../../../atoms/CompanyIcon/IconsList';
import StockPriceChange from '../../../../molecules/StockPriceChange';
import { useStockAndInstrumentFeed } from '../../../../../utils/Equities/hooks';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import ColoredMeter from '../../../../../assets/icons/colored-meter.svg';
import styles from './index.scss';

const ExpertPickCard = ({
  index,
  scripsList,
  scripData,
  totalCards = 0,
  isFullPage = false,
  showExpertPicksWidgetPopupHandler = () => {},
}) => {
  const { ltp, pClose } = useStockAndInstrumentFeed({
    exchange: scripData.exchange,
    segment: scripData.segment,
    securityId: scripData.security_id,
    instrumentType: scripData.instrument_type,
    id: scripData.id,
  });

  const rangeValue = useMemo(() => {
    if (pClose && ltp) {
      return ((ltp - pClose) / Math.abs(pClose)) * 100;
    }
    return null;
  }, [ltp, pClose]);

  const getTitleClassName = () => cx(styles.title, {});

  const getCardClassName = () =>
    cx(styles.card, {
      [styles.firstFullPageCard]: isFullPage && index === 0,
      [styles.partialCard]: !isFullPage,
      [styles.lastCard]:
        !isFullPage &&
        (index === totalCards - 1 || index === scripsList.length - 1),
    });

  return (
    <div
      className={getCardClassName()}
      key={scripData.id}
      onClick={showExpertPicksWidgetPopupHandler}
    >
      <div className={styles.headerWrapper}>
        <div className={styles.companyDetails}>
          <CompanyIcon
            name={scripData.id}
            type={COMPANY_ICONS_NAME.STOCKS}
            className={styles.companyIcon}
          />
          <div className={styles.stockHeader}>
            <div className={getTitleClassName()}>{scripData.name}</div>
            <div className={styles.companyName}>
              <div className={styles.returnsWrapper}>
                <StockPriceChange
                  fromComponent="experts-pick"
                  securityId={scripData.security_id}
                  exchange={scripData.exchange}
                  segment={scripData.segment}
                  change={rangeValue}
                  isSymbol={false}
                  withRupee={false}
                  customClassName={styles.stockPriceChange}
                />
              </div>
            </div>
          </div>
          <div>
            <Icon name={ICONS_NAME.RIGHT_ARROW} width={6} height={10} />
          </div>
        </div>
      </div>
      <div className={styles.descriptionWrapper}>
        <div className={styles.expertsRecommendation}>
          <Icon name={ICONS_NAME.SHIELD_TICK} width={11} />
          <span className={styles.expertsRecommendationText}>
            100% Experts recommend Buy
          </span>
        </div>
        <div>
          <img src={ColoredMeter} alt="expert-meter" />
        </div>
      </div>
    </div>
  );
};

export default ExpertPickCard;
