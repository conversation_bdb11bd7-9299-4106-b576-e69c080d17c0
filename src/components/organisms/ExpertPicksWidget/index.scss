.mainContainer {
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    justify-content: space-between;
    gap: 4px;
    align-items: start;
    flex-shrink: 0;
    margin: 0 15px;

    .text {
      .title {
        color: var(--text-neutral-strong);
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
      }
      .subtitle {
        font-size: 12px;
        color: var(--text-neutral-weak);
        font-weight: 400;
        line-height: 16px;
      }
    }
  }
  .viewAllCard {
    min-width: max-content;
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 14px;
    color: var(--text-primary-strong);
    font-weight: 500;
    line-height: 20px;
  }
}
