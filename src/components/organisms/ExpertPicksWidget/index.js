/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState } from 'react';
import cx from 'classnames';
import {
  openDeepLinkPaytmMoney,
  // exitApp,
} from '@src/utils/bridgeUtils';
import { log } from '../../../utils/commonUtil';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { expertPicksWidgetListDeeplink } from '../../../utils/constants';
import Carousel from '../../molecules/Carousel';
import ExpertPickCard from './partials/ExpertPickCard';
import { getExpertPicksFilters, getScripsList } from './utils';
import styles from './index.scss';

const ExpertPicksWidget = (props) => {
  const {
    data: widgetData,
    aggrKey,
    navigateTo,
    history,
    miniAppRoutes: MINI_APP_ROUTES,
    businessTypeFallback,
    showExpertPicksWidgetPopupHandler = () => {},
  } = props;
  log('widgetData: ', widgetData);

  const NUMBER_OF_CARDS_TO_SHOW = 5;

  const [isFullPage, setIsFullPage] = useState(false);
  const [scripsList, setScripsList] = useState([]);

  useEffect(() => {
    const fetchExpertPicksFilters = async () => {
      try {
        const expertPicksData = await getExpertPicksFilters();

        if (expertPicksData) {
          try {
            const scripsData = await getScripsList(expertPicksData);
            setScripsList(scripsData?.data?.results);
          } catch (scripsError) {
            console.error('Error fetching scrips list:', scripsError);
            setScripsList([]);
          }
        }
      } catch (error) {
        console.error('Error fetching expert picks:', error);
      }
    };

    fetchExpertPicksFilters();
  }, []);

  const handleViewAllClick = () => {
    if (isPaytmMoney()) {
      const deeplink = `${expertPicksWidgetListDeeplink}&businessType=${businessTypeFallback}&aggrKey=${aggrKey}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      setIsFullPage(true);
    }
  };

  // todo
  // const handleBackClick = () => {
  //   if (isPaytmMoney()) {
  //     exitApp();
  //   } else {
  //     setIsFullPage(false);
  //   }
  // };

  if (isFullPage) {
    if (!isPaytmMoney()) {
      navigateTo(history, MINI_APP_ROUTES.FLUTTER_APP, {}, 'push');
    }
    return null;
  }

  return (
    <div className={cx(styles.mainContainer)}>
      <div className={styles.header}>
        <div className={styles.text}>
          <div className={styles.title}>Expert Recommendations</div>
          <div className={styles.subtitle}>
            Verified research, aggregated by Refinitiv
          </div>
        </div>
        {!isFullPage && (
          <div className={styles.viewAllCard} onClick={handleViewAllClick}>
            <div>View All</div>
          </div>
        )}
      </div>
      <Carousel
        customStyle={styles.bannersCarousel}
        customSetting={{
          slidesToShow: 1,
          slidesToScroll: 1,
          autoplay: false,
          infinite: false,
          variableWidth: true,
          swipeToSlide: true,
          speed: 300,
          arrows: false,
          dots: false,
        }}
      >
        {scripsList
          ?.slice(0, NUMBER_OF_CARDS_TO_SHOW)
          .map((scripData, index) => (
            <ExpertPickCard
              key={index}
              index={index}
              scripData={scripData}
              scripsList={scripsList}
              isFullPage={isFullPage}
              totalCards={NUMBER_OF_CARDS_TO_SHOW}
              showExpertPicksWidgetPopupHandler={() =>
                showExpertPicksWidgetPopupHandler(scripData)
              }
            />
          ))}
      </Carousel>
    </div>
  );
};

export default React.memo(ExpertPicksWidget);
