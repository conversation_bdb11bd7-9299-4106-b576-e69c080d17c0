@import '/src/commonStyles/variables.scss';

.expertPicksInfoPopupHeader {
  .headerImage {
    width: 100vw;
    height: auto;
  }
}

.contentContainer {
  padding: 16px;
  padding-bottom: 0;
  color: var(--icon-neutral-strong);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.hr {
  border-bottom: 1px solid var(--border-neutral-variant);
}

.title {
  .titleMain {
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    letter-spacing: -0.01px;
  }
}

.sectionTitle {
  font-weight: 600;
  line-height: 22px;
}

.desc {
  font-size: 12px;
  color: var(--text-neutral-medium);
}

.textLight {
  color: var(--text-neutral-medium);
}

.section {
  ul {
    list-style: disc;
    margin-left: 18px;
    margin-bottom: 0;
    padding: 0;
    color: var(--text-neutral-medium);

    li {
      font-size: 12px;
      font-weight: 400;
    }

    .liBold {
      color: var(--icon-neutral-strong);
      font-size: 12px;
      font-weight: 500;
    }
  }
}
