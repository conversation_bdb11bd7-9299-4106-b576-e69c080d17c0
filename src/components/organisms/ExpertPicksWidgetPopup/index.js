/* eslint-disable no-underscore-dangle */
import React from 'react';
import Drawer from '../../molecules/Drawer/Drawer';
import EPHeader from './_partials/EPHeader';
import EPRiskMeter from './_partials/EPRiskMeter';
import EPFooter from './_partials/EPFooter';
import ExpertOpinion from './_partials/ExpertOpinion';
import styles from './index.scss';

const ExpertPicksWidgetPopup = ({
  isOpen = false,
  onClose,
  scripData,
  navigateTo,
  history,
}) => {
  const closePopup = () => {
    onClose();
  };

  return (
    <Drawer
      active={isOpen}
      showCloseIcon={false}
      showCustomCloseIcon
      triggerClose={closePopup}
      // customClass={styles.drawer}
    >
      <div className={styles.popupContainer}>
        <EPHeader
          scripData={scripData}
          navigateTo={navigateTo}
          history={history}
        />

        <div className={styles.midContainer}>
          <EPRiskMeter scripData={scripData} />
          <ExpertOpinion scripData={scripData} />
        </div>

        <EPFooter
          navigateTo={navigateTo}
          history={history}
          scripData={scripData}
        />
      </div>
      {/* <EPFooter
        navigateTo={navigateTo}
        history={history}
        scripData={scripData}
      /> */}
    </Drawer>
  );
};

export default React.memo(ExpertPicksWidgetPopup);
