.epPopupHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  padding-top: 0;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-pop-up);
  margin: 0;
  box-sizing: border-box;
  flex-shrink: 0;

  .epHeaderContent {
    margin-top: 0px;
    display: flex;
    align-items: center;
    padding: 0;
    gap: 12px;
    min-width: 0;

    .companyLogo {
      .companyIcon {
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 38px;
        min-height: 38px;
        border-radius: 8px;
        overflow: hidden;

        > img {
          width: 30px;
          height: 30px;
        }
      }
    }

    .titleSection {
      min-width: 0;
      max-width: 70vw;

      .titleRow {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 2px;
        width: 100%;

        .title {
          color: var(--icon-neutral-strong);
          font-size: 16px;
          font-weight: 500;
          letter-spacing: -0.01em;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .priceInfo {
        display: flex;
        align-items: baseline;
        width: max-content;

        .price {
          color: var(--icon-neutral-strong);
          font-size: 14px;
          font-weight: 400;
        }

        .change {
          font-size: 14px;
          font-weight: 400;

          &.positive {
            color: rgba(34, 197, 94, 0.95);
          }

          &.negative {
            color: rgba(239, 68, 68, 0.95);
          }
        }
      }
    }
  }
}
