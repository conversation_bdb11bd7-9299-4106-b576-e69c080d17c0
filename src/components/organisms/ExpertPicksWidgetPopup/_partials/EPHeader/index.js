import { useCallback } from 'react';
import ArrowLeftIcon from '@paytm-h5-common/paytm_common_ui/icons/system/nav/ArrowLeft';
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import StockChange from '../../../StockChange/StockChange';
import { isPaytmMoney } from '../../../../../utils/coreUtil';
import { openDeepLinkPaytmMoney } from '../../../../../utils/bridgeUtils';
import { log } from '../../../../../utils/commonUtil';
import styles from './index.scss';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';

const EPHeader = ({ scripData = {}, navigateTo, history }) => {
  const companyPageNavigation = useCallback(
    (stockNewsItem = {}) => {
      log('stockNewsItem', stockNewsItem);
      const { id, instrument_type } = stockNewsItem;
      const instrumentType =
        instrument_type === 'ES' ? 'company' : instrument_type?.toLowerCase();

      if (isPaytmMoney()) {
        const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?shouldOpenOnCurrentScreen=true`;
        openDeepLinkPaytmMoney(url);
      } else {
        navigateTo(history, `/company-revamp?id=${id}`, {}, 'push');
      }
    },
    [history, navigateTo],
  );

  return (
    <div className={styles.epPopupHeader}>
      <div
        className={styles.epHeaderContent}
        onClick={() => {
          companyPageNavigation(scripData);
        }}
      >
        <div className={styles.companyLogo}>
          <CompanyIcon
            name={scripData.id}
            type="stocks"
            url={scripData.companyLogo}
            fallbackImg={FallbackCompanyIcon}
            className={styles.companyIcon}
          />
        </div>
        <div className={styles.titleSection}>
          <div className={styles.titleRow}>
            <span className={styles.title}>{scripData.name}</span>
            <div>
              <Icon name={ICONS_NAME.ARROW_RIGHT} />
            </div>
          </div>
          <div className={styles.priceInfo}>
            <span className={styles.price}>{scripData.price}</span>
            <StockChange
              exchange={scripData.exchange}
              segment={scripData.segment}
              securityId={scripData.security_id}
              instrumentType={scripData.instrument}
              id={scripData.id}
              showLtp
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EPHeader;
