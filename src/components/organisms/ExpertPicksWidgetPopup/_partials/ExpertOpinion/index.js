import cx from 'classnames';
import { isDarkMode } from '../../../../../utils/commonUtil';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import styles from './index.scss';

const ExpertOpinion = ({ scripData = {} }) => {
  console.log('scripData', scripData);

  const title =
    scripData.buyPer === 100.0
      ? 'Super Pick! All experts are in.'
      : scripData.buyPer != null && scripData.buyPer >= 90.0
        ? 'Top Expert Favourite'
        : scripData.buyPer != null && scripData.buyPer >= 80.0
          ? 'Strong Buy Consensus'
          : 'Positive Outlook';

  const subtitle =
    scripData.buyPer === 100.0
      ? 'Every expert tracking this stock says Buy'
      : scripData.buyPer != null &&
          scripData.numBuy != null &&
          scripData.numStrBuy != null &&
          scripData.numRecs != null &&
          scripData.buyPer >= 90.0
        ? `Near perfect score, ${scripData.numBuy + scripData.numStrBuy} experts say Buy`
        : scripData.buyPer != null && scripData.buyPer >= 80.0
          ? 'Majority of experts see strong upside in this stock'
          : 'Majority of experts are optimistic about this stock';

  function roundToTwo(num) {
    if (!num) return 0;
    return parseFloat(num.toFixed(0));
  }

  return (
    <div className={styles.expertOpinionContainer}>
      <div
        className={cx(styles.mainWrapper, {
          [styles.mainWrapperDarkMode]: isDarkMode(),
        })}
      />
      <div className={styles.expertOpinionHeader}>
        <div className={styles.expertOpinionTitle}>
          <Icon name={ICONS_NAME.SUPER_PICK_STARS} size={4} />
          <span
            className={cx(styles.superPickText, {
              [styles.superPickTextDarkMode]: isDarkMode(),
            })}
          >
            {title}
          </span>
        </div>
        <div className={styles.expertOpinionSubTitle}>{subtitle}</div>
      </div>
      <div className={styles.horizontalLine} />
      <div className={styles.content}>
        <div className={styles.barProgress}>
          <div className={styles.barItem}>
            <div className={styles.barLabelContainer}>
              <div className={styles.label}>Buy</div>
              <div className={styles.barContainer}>
                <div
                  className={styles.bar}
                  style={{
                    width: `${roundToTwo(scripData.buy_per)}%`,
                    background: '#2cb079',
                  }}
                />
              </div>
              <div className={styles.percent}>
                {roundToTwo(scripData.buy_per)}%
              </div>
              {/* <div className={styles.labelContainer}></div> */}
            </div>
          </div>
          <div className={styles.barItem}>
            <div className={styles.barLabelContainer}>
              <div className={styles.label}>Hold</div>
              <div className={styles.barContainer}>
                <div
                  className={styles.bar}
                  style={{
                    width: `${roundToTwo(scripData.hold_per)}%`,
                    background: '#FED533',
                  }}
                />
              </div>
              <div className={styles.percent}>
                {roundToTwo(scripData.hold_per)}%
              </div>
              {/* <div className={styles.labelContainer}></div> */}
            </div>
          </div>
          <div className={styles.barItem}>
            <div className={styles.barLabelContainer}>
              <div className={styles.label}>Sell</div>
              <div className={styles.barContainer}>
                <div
                  className={styles.bar}
                  style={{
                    width: `${roundToTwo(scripData.sell_per)}%`,
                    background: '#E63757',
                  }}
                />
              </div>
              <div className={styles.percent}>
                {roundToTwo(scripData.sell_per)}%
              </div>
              {/* <div className={styles.labelContainer}></div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpertOpinion;
