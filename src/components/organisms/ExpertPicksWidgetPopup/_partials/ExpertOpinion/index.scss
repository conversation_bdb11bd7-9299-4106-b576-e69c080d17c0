.expertOpinionContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-neutral-weak);
  border-radius: 16px;
  overflow: hidden;
  color: var(--background-neutral-strong);

  .content {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 16px;
    z-index: 2;

    .barProgress {
      display: flex;
      flex-direction: column;
      gap: 20px;
      justify-content: space-between;
      width: 100%;

      .barItem {
        display: flex;
        align-items: end;
        gap: 12px;
        justify-content: space-between;

        .bar {
          height: 5px;
          border-radius: 100px;
        }
      }
    }
  }

  .targetPrice {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 0 16px;

    .targetLabel {
      font-family: Inter;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #1010108a;
    }

    .targetValues {
      display: flex;
      gap: 20px;
      justify-content: space-between;

      .targetItem {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .targetValue {
          font-size: 14px;
          font-family: Inter;
          font-weight: 500;
          color: #101010;
        }

        .lowTargetValue {
          font-size: 14px;
          font-family: Inter;
          font-weight: 500;
          color: #e63757;
        }

        .highTargetValue {
          font-size: 14px;
          font-family: Inter;
          font-weight: 500;
          color: #52cb77;
        }
      }
    }
  }

  .footer {
    background-color: #ecf2f8;
    padding: 7px 12px;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Inter;
    font-weight: 400;
    font-size: 10px;
    line-height: 16px;

    .infoIcon {
      cursor: pointer;
    }
  }

  .footerText {
    display: flex;
    gap: 8px;
  }

  .infoPopup {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }
}

.targetTitle {
  font-family: Inter;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  color: #101010;
  padding-top: 15px;
  border-top: 1px solid #f5f0f0;
}

.barContainer {
  background-color: var(--background-neutral-weak);
  flex: 1;
  border-radius: 5px;
  overflow: hidden;
}

.barLabelContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  width: 100%;

  .label,
  .percent {
    white-space: nowrap;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  .label {
    width: 32px;
  }
  .percent {
    width: 37px;
    text-align: right;
  }

  .labelContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    gap: 4px;
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #101010;
  }
}
.expertOpinionHeader {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  z-index: 2;
}

.expertOpinionTitle {
  width: fit-content;
  display: flex;
  justify-content: start;
  gap: 4px;
}

.superPickText {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  background: linear-gradient(
    90deg,
    var(--Vivid-Blue, #2f81ed) 0%,
    var(--Text-link, #013da6) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.superPickTextDarkMode {
  background: linear-gradient(
    90deg,
    var(--Vivid-Blue, #2361b2) 0%,
    var(--Text-link, #0a86bf) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.expertOpinionSubTitle {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
}

.horizontalLine {
  border-bottom: 1px solid var(--border-neutral-weak);
  z-index: 2;
}

.mainWrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    122.2deg,
    #e0f5fd 23.5%,
    #d3d4ff 46.41%,
    #d5e8ff 69.32%
  );
  opacity: 0.5;
  filter: blur(30px);
  z-index: 1;
}

.mainWrapperDarkMode {
  background: linear-gradient(
    122.2deg,
    #0f303d 23.5%,
    #1c1157 46.41%,
    #001128 69.32%
  );
}
