@import '../../../../../commonStyles/variables.scss';
@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.riskMeterContainer {
  display: flex;
  padding-top: 8px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  border-radius: 12px;
  border: 1px solid var(--border-neutral-variant);
  background: var(--surface-level-1);

  .mainBox {
    position: relative;
    display: inline-block;
    .riskDiv {
      display: block;
    }
    .needleContainer {
      position: absolute;
      top: 78%;
      left: 50%;
      .needleIcon {
        transform-origin: 0%;
      }
    }
  }
  .expertlist {
    display: flex;
    padding: 8px 0;
    justify-content: center;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    border-top: 1px solid var(--border-neutral-variant);

    .expertText {
      color: var(--border-neutral-medium);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
    }
    .infoIcon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
