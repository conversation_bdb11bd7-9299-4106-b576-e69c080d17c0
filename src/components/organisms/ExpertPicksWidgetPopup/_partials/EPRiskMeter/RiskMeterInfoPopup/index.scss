.drawer {
  padding: 0 !important;
}

.popupContainer {
  width: 100%;
  height: 100%;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-pop-up);
  overflow-y: auto;
  margin: 0 !important;
  padding: 0;
  box-sizing: border-box;
  .midContainer {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.customCloseIconClass {
  top: 16px !important;
  right: 16px !important;
}
