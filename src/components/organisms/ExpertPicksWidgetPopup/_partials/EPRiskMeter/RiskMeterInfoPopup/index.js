/* eslint-disable no-underscore-dangle */
import React from 'react';
import Drawer from '../../../../../molecules/Drawer/Drawer';
import ExpertPicksInfoPopup from '../../../../ExpertPicksInfoPopup';
import styles from './index.scss';

const RiskMeterInfoPopup = ({ isOpen = false, onClose }) => {
  const closePopup = () => {
    onClose();
  };

  return (
    <Drawer
      active={isOpen}
      customClass={styles.drawer}
      showCloseIcon={false}
      showCustomCloseIcon
      triggerClose={closePopup}
      customCloseIconWidth="32px"
      customCloseIconClass={styles.customCloseIconClass}
    >
      <div className={styles.popupContainer}>
        <ExpertPicksInfoPopup />
      </div>
    </Drawer>
  );
};

export default React.memo(RiskMeterInfoPopup);
