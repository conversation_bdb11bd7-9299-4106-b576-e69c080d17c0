import needleIconLight from '../../../../../assets/images/needleIconLight.svg';
import needleIconDark from '../../../../../assets/images/needleIconDark.svg';
import meterIconLight from '../../../../../assets/images/meterIconLight.svg';
import meterIconDark from '../../../../../assets/images/meterIconDark.svg';
import infoIconLight from '../../../../../assets/images/infoIconLight.svg';
import infoIconDark from '../../../../../assets/images/infoIconDark.svg';

import styles from './index.scss';
import { isDarkMode } from '../../../../../utils/commonUtil';
import { useDrawer } from '../../../../molecules/Drawer/useDrawer';
import RiskMeterInfoPopup from './RiskMeterInfoPopup';

const EPRiskMeter = ({ scripData }) => {
  const EXPERTS_COUNT = scripData.num_recs;

  const {
    isOpen: showRiskMeterInfoPopup,
    onOpen: onRiskMeterInfoPopupOpen,
    onClose: onRiskMeterInfoPopupClose,
  } = useDrawer();

  const getNeedleAngle = () => ((scripData.mean - 1) / 4) * -180;

  const handleInfoIconClick = () => {
    onRiskMeterInfoPopupOpen();
  };

  return (
    <>
      <div className={styles.riskMeterContainer}>
        <div className={styles.mainBox}>
          <div className={styles.riskDiv}>
            <img
              className={styles.meterIcon}
              src={isDarkMode() ? meterIconDark : meterIconLight}
              alt="meter Icon"
            />
          </div>
          <div className={styles.needleContainer}>
            <img
              className={styles.needleIcon}
              src={isDarkMode() ? needleIconDark : needleIconLight}
              alt="needle icon"
              style={{
                transform: `rotate(${getNeedleAngle()}deg)`,
              }}
            />
          </div>
        </div>
        <div className={styles.expertlist}>
          <div className={styles.expertText}>
            {`Rated by ${EXPERTS_COUNT} Experts`}
          </div>
          <div className={styles.infoIcon} onClick={handleInfoIconClick}>
            <img
              className={styles.infoIcon}
              src={isDarkMode() ? infoIconDark : infoIconLight}
              alt="meter Icon"
            />
          </div>
        </div>
      </div>
      <RiskMeterInfoPopup
        isOpen={showRiskMeterInfoPopup}
        onClose={onRiskMeterInfoPopupClose}
      />
    </>
  );
};

export default EPRiskMeter;
