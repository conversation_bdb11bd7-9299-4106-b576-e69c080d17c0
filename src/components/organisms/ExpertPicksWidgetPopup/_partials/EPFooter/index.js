import styles from './index.scss';
import { generateQueryParamsString } from '../../../../../utils/commonUtil';

const EPFooter = ({ scripData, navigateTo, history }) => {
  const {
    id,
    security_id,
    exchange,
    name,
    segment,
    isin,
    instrument_type,
    tick_size,
  } = scripData || {};

  const queryString = generateQueryParamsString({
    id,
    securityId: security_id,
    exchange,
    name,
    segment,
    isin,
    tickSize: tick_size,
    instrumentType: instrument_type,
    symbol: null,
    activeLocation: 'company-page',
    quantity: 0,
    lotSize: null,
    product: 'C',
  });

  const ctaClick = () => {
    // if (isPaytmMoney()) {
    //   const instrumentType =
    //     instrument === 'ES' ? 'company' : instrument.toLowerCase();
    //   const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?action=place-order&txn_type=${transactionType}&price=0&product=${product}&order_type=MKT&exchange=${exchange}&shouldOpenOnCurrentScreen=true`;
    //   openDeepLinkPaytmMoney(url);
    // } else {
    navigateTo(history, `/order-pad${queryString}`, {}, 'push');
    // }
  };

  return (
    // <div className={styles.footer}>
    <div className={styles.footerCta} onClick={ctaClick}>
      Buy {name || ''}
    </div>
    // </div>
  );
};

export default EPFooter;
