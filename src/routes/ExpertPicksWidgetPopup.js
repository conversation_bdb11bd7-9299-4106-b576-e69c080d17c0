import { useEffect } from 'react';
import queryString from 'query-string';
// import { sendErrorToBackend } from '../actions/runtime';
import AggregatorComponent from '../HOC/Aggregator/AggregatorComponent';
import { BUSINESS_TYPE_MAPPINGS } from '../utils/constants';
import { AGGREGATOR_API } from '../config/urlConfig';
import CentralLoader from '../components/atoms/CentralLoader/CentralLoader';
import ExpertPicksWidgetPopupPage from '../pages/ExpertPicksWidgetPopupPage';

const ExpertPicksWidgetPopupWrapper = () => (
  //   if (!pages.data) {
  //     try {
  //       sendErrorToBackend({
  //         level: 'error',
  //         key: 'expert-picks-widget-popup-no-data',
  //         timestamp: new Date().toISOString(),
  //         data: JSON.stringify({}),
  //       });
  //       return null;
  //     } catch (err) {
  //       console.error('Error logging failed', err);
  //       return null;
  //     }
  //   }

  <ExpertPicksWidgetPopupPage />
);

const ExpertPicksPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const {
    // cohort = '',
    // widgetType = '',
    aggrKey,
    businessType,
    widgetId,
  } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';
  }, []);

  const expertPicksWidgetPopupData = {};
  //   localStorage.getItem(LOCAL_STORAGE.EXPERT_PICKS_POPUP_DATA);

  if (expertPicksWidgetPopupData) {
    const parsedData = JSON.parse(expertPicksWidgetPopupData);

    return (
      <ExpertPicksWidgetPopupWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId,
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ExpertPicksWidgetPopup',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(ExpertPicksWidgetPopupWrapper);

  return <WrappedComponent />;
};

export default ExpertPicksPopupPageRoute;
