/* eslint-disable arrow-body-style */
import { useEffect } from 'react';
// import queryString from 'query-string';
// import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
// import { AGGREGATOR_API } from '../../config/urlConfig';
// import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
// import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';

const ExpertPicksWidgetListWrapper = () => {
  useEffect(() => {
    // Any additional setup for list page
  }, []);

  return 'Expert Picks Widget List';
};

const ExpertPicksWidgetListPageRoute = () => {
  // const query = queryString.parse(window.location?.search);
  // const { aggrKey, businessType } = query || {};

  // if (localStorage.getItem('expertPicksWidgetData')) {
  //   const parsedData = JSON.parse(
  //     localStorage.getItem('expertPicksWidgetData'),
  //   );
  return <ExpertPicksWidgetListWrapper />;
  // }

  // if (!aggrKey) {
  //   return null;
  // }

  // const WrappedComponent = AggregatorComponent({
  //   queryProps: {
  //     name: 'ExpertPicksWidgetList',
  //     url:
  //       BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
  //       AGGREGATOR_API.COMBINED_DASHBOARD,
  //     fallbackUrl:
  //       BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
  //       AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
  //     queryParams: {
  //       keys: aggrKey,
  //     },
  //   },
  //   loader: <CentralLoader />,
  // })(ExpertPicksWidgetListWrapper);

  // return <WrappedComponent />;
};

export default ExpertPicksWidgetListPageRoute;
