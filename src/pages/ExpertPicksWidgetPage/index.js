import { useEffect, useRef, useState } from 'react';
import queryString from 'query-string';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import {
  getStartupParamsAllCallback,
  openDeepLinkPaytmMoney,
} from '../../utils/bridgeUtils';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
// import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import {
  BUSINESS_TYPE_MAPPINGS,
  expertPicksWidgetPopupDeeplink,
} from '../../utils/constants';
import { log } from '../../utils/commonUtil';
import ExpertPicksWidget from '../../components/organisms/ExpertPicksWidget';
import ExpertPicksWidgetPopup from '../../components/organisms/ExpertPicksWidgetPopup';
import { useDrawer } from '../../components/molecules/Drawer/useDrawer';
import { isPaytmMoney } from '../../utils/coreUtil';

export const ExpertPicksWidgetPageContentWrapper = (props) => {
  const {
    data,
    pages,
    businessType,
    aggrKey,
    navigateTo,
    history,
    miniAppRoutes,
  } = props;

  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const [popupScrip, setPopupScrip] = useState({});
  const expertPicksWidgetCardRef = useRef(null);

  const {
    isOpen: showExpertPicksWidgetPopup,
    onOpen: onExpertPicksWidgetPopupOpen,
    onClose: onExpertPicksWidgetPopupClose,
  } = useDrawer();

  const showExpertPicksWidgetPopupHandler = (scrip) => {
    if (!isPaytmMoney()) {
      onExpertPicksWidgetPopupOpen();
      setPopupScrip(scrip);
    } else {
      openDeepLinkPaytmMoney(expertPicksWidgetPopupDeeplink);
    }
  };

  // useNotifyHeight(
  //   expertPicksWidgetCardRef,
  //   data?.widgetId || aggrData?.widgetId,
  //   {
  //     flowType:
  //       BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
  //         ?.height || 'combinedHomeH5FragmentHeight',
  //   },
  // );

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  // todo ==> data || aggrData ? () : null;
  return (
    <div ref={expertPicksWidgetCardRef}>
      <ExpertPicksWidget
        data={data || aggrData}
        aggrKey={aggrKey}
        navigateTo={navigateTo}
        history={history}
        miniAppRoutes={miniAppRoutes}
        businessTypeFallback={businessType || pages?.data?.businessType}
        showExpertPicksWidgetPopupHandler={showExpertPicksWidgetPopupHandler}
      />
      <ExpertPicksWidgetPopup
        isOpen={showExpertPicksWidgetPopup}
        onClose={onExpertPicksWidgetPopupClose}
        scripData={popupScrip}
        navigateTo={navigateTo}
        history={history}
      />
    </div>
  );
};

const ExpertPicksWidgetPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};
  console.log('{ businessType, aggrKey }', { businessType, aggrKey });

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        localStorage.setItem(
          'expertPicksWidgetData',
          JSON.stringify(parsedData),
        );
        setNativeData(parsedData);
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return 'loading...';
  }

  if (nativeData) {
    return (
      <ExpertPicksWidgetPageContentWrapper
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ExpertPicksWidget',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: null, // Could add a loader component here
  })(ExpertPicksWidgetPageContentWrapper);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export default withErrorBoundary(ExpertPicksWidgetPage);
